"use client";

import { useState, useMemo } from "react";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  ExternalLink, 
  Edit,
  Trash2,
  LinkIcon,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  BarChart3,
  TrendingUp,
  RefreshCw,
  Search,
  Lock
} from "lucide-react";
import { Link } from "@/types/links";
import { toast } from "sonner";
import { DeleteConfirmationDialog } from "./delete-confirmation-dialog";
import { getCanonicalDomain } from "@/utils/url-normalization";
import { useTierStatus } from "@/lib/hooks/useTierStatus";

interface LinksTableProps {
  links: Link[];
  loading: boolean;
  onEditLink: (link: Link) => void;
  onDeleteLink: (link: Link) => void;
  onAddLink: () => void;
  onBatchUpdateDR?: () => Promise<void>;
  onBatchUpdateTraffic?: () => Promise<void>;
}

type FilterType = 'all' | 'paid' | 'free';
type SortField = 'dr_score' | 'traffic' | 'none';
type SortOrder = 'asc' | 'desc';

export function LinksTable({
  links,
  loading,
  onEditLink,
  onDeleteLink,
  onAddLink,
  onBatchUpdateDR,
  onBatchUpdateTraffic
}: LinksTableProps) {
  const t = useTranslations("links");
  const { canMakeDrQuery, canMakeTrafficUpdate, isPaidUser } = useTierStatus();
  
  // Batch update loading states
  const [updatingDR, setUpdatingDR] = useState(false);
  const [updatingTraffic, setUpdatingTraffic] = useState(false);
  const [discoveringLinks, setDiscoveringLinks] = useState(false);
  
  // Helper function to safely extract domain from URL using canonical domain extraction
  const getDomain = (link: Link) => {
    if (link.domain) return link.domain;
    return getCanonicalDomain(link.url) || link.url.replace(/^https?:\/\//, '').split('/')[0];
  };
  
  const [filterType, setFilterType] = useState<FilterType>('all');
  const [sortField, setSortField] = useState<SortField>('none');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');

  // Batch update handlers
  const handleBatchUpdateDR = async () => {
    if (!onBatchUpdateDR || updatingDR) return;
    
    if (!canMakeDrQuery) {
      toast.error("DR queries are not available for free users. Please upgrade to Professional plan.");
      return;
    }
    
    setUpdatingDR(true);
    try {
      await onBatchUpdateDR();
      toast.success("Domain Rating data updated successfully");
    } catch (error) {
      console.error("Error updating DR data:", error);
      toast.error("Failed to update Domain Rating data");
    } finally {
      setUpdatingDR(false);
    }
  };

  const handleBatchUpdateTraffic = async () => {
    if (!onBatchUpdateTraffic || updatingTraffic) return;
    
    if (!canMakeTrafficUpdate) {
      toast.error("Traffic updates are not available for free users. Please upgrade to Professional plan.");
      return;
    }
    
    setUpdatingTraffic(true);
    try {
      await onBatchUpdateTraffic();
      toast.success("Traffic data updated successfully");
    } catch (error) {
      console.error("Error updating traffic data:", error);
      toast.error("Failed to update traffic data");
    } finally {
      setUpdatingTraffic(false);
    }
  };

  const filteredAndSortedLinks = useMemo(() => {
    let filtered = links;
    
    // Apply type filter
    if (filterType !== 'all') {
      filtered = links.filter(link => link.link_type === filterType);
    }
    
    // Apply sorting
    if (sortField !== 'none') {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[sortField];
        const bValue = b[sortField];
        
        if (sortOrder === 'asc') {
          return aValue - bValue;
        } else {
          return bValue - aValue;
        }
      });
    }
    

    
    return filtered;
  }, [links, filterType, sortField, sortOrder]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle sort order
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort field with default desc order
      setSortField(field);
      setSortOrder('desc');
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { variant: "default" | "secondary" | "destructive" | "outline", label: string }> = {
      pending: { variant: "outline", label: "Pending" },
      active: { variant: "default", label: "Active" },
      inactive: { variant: "secondary", label: "Inactive" },
      removed: { variant: "destructive", label: "Removed" }
    };
    
    const config = statusConfig[status] || { variant: "outline", label: status };
    
    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="bg-background border border-border rounded-lg p-8">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading links...</p>
          </div>
        </div>
      </div>
    );
  }

  if (links.length === 0) {
    return (
      <div className="bg-background border border-border rounded-lg p-12">
        <div className="text-center">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
            <LinkIcon className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">No links found</h3>
          <p className="text-muted-foreground mb-6">
            Start by adding your first backlink to track its performance.
          </p>
          <Button onClick={onAddLink}>
            <LinkIcon className="h-4 w-4 mr-2" />
            Add Your First Link
          </Button>
        </div>
      </div>
    );
  }

  // Mobile card view component
  const MobileCardView = () => (
    <div className="grid gap-4 w-full">
      {filteredAndSortedLinks.map((link) => (
        <Card key={link.id} className="hover:shadow-md transition-shadow w-full">
          <CardContent className="p-4 w-full">
            <div className="space-y-3 w-full">
              {/* Header with title and external link */}
              <div className="flex items-start justify-between gap-2 w-full">
                <div className="flex-1 min-w-0 overflow-hidden">
                  <h3 className="font-medium text-foreground truncate text-sm sm:text-base">
                    {link.title}
                  </h3>
                  <a 
                    href={`https://${getDomain(link)}`}
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-xs sm:text-sm text-muted-foreground hover:text-primary transition-colors truncate block"
                  >
                    {getDomain(link)}
                  </a>
                </div>
                <a 
                  href={link.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors flex-shrink-0"
                >
                  <ExternalLink className="h-4 w-4" />
                </a>
              </div>

              {/* Stats row */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 w-full">
                <div className="flex items-center gap-3 sm:gap-4 flex-wrap">
                  <div className="flex items-center gap-1">
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    <span className="text-xs sm:text-sm font-medium">DR {link.dr_score}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    <span className="text-xs sm:text-sm font-medium">{link.traffic?.toLocaleString() || '0'}</span>
                  </div>
                </div>
                <Badge variant={link.link_type === 'paid' ? 'default' : 'secondary'} className="text-xs w-fit">
                  {link.link_type === 'paid' ? 'Paid' : 'Free'}
                </Badge>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end gap-2 pt-2 border-t w-full">
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => onEditLink(link)}
                  className="flex items-center gap-1 text-xs sm:text-sm px-2 sm:px-3"
                >
                  <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">Edit</span>
                </Button>
                <DeleteConfirmationDialog
                  link={link}
                  onDelete={onDeleteLink}
                  trigger={
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="flex items-center gap-1 text-xs sm:text-sm px-2 sm:px-3 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden xs:inline">Delete</span>
                    </Button>
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      {/* Filter and Sort Controls */}
      <div className="flex flex-col gap-4 p-3 sm:p-4 bg-muted/20 rounded-lg w-full">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 w-full">
          <div className="flex flex-col xs:flex-row xs:items-center gap-2 w-full xs:w-auto">
            <span className="text-xs sm:text-sm font-medium whitespace-nowrap">Filter by Type:</span>
            <Select value={filterType} onValueChange={(value: FilterType) => setFilterType(value)}>
              <SelectTrigger className="w-full xs:w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="free">Free</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex flex-col xs:flex-row xs:items-center gap-2 w-full xs:w-auto">
            <span className="text-xs sm:text-sm font-medium whitespace-nowrap">Sort by:</span>
            <Button
              variant={sortField === 'none' ? 'default' : 'outline'}
              size="sm"
              onClick={() => {
                setSortField('none');
              }}
              className="w-full xs:w-auto"
            >
              Default
            </Button>
          </div>
          
          <div className="text-xs sm:text-sm text-muted-foreground sm:ml-auto order-first sm:order-last">
            Showing {filteredAndSortedLinks.length} of {links.length} links
          </div>
        </div>
        
        {/* Batch Update Controls */}
        {(onBatchUpdateDR || onBatchUpdateTraffic) && (
          <div className="flex flex-col xs:flex-row xs:items-center gap-2 pt-2 border-t">
            <span className="text-xs sm:text-sm font-medium text-muted-foreground">Batch Updates:</span>
            <div className="flex gap-2 flex-wrap">
              {onBatchUpdateDR && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchUpdateDR}
                  disabled={updatingDR || updatingTraffic || discoveringLinks || !canMakeDrQuery}
                  className="flex items-center gap-1"
                  title={!canMakeDrQuery ? "DR queries are available for Professional users only" : "Update Domain Rating data for all links"}
                >
                  {!canMakeDrQuery && <Lock className="h-3 w-3" />}
                  {canMakeDrQuery && <RefreshCw className={`h-3 w-3 ${updatingDR ? 'animate-spin' : ''}`} />}
                  <span className="text-xs">
                    {!canMakeDrQuery ? 'Pro Only' : updatingDR ? 'Updating DR...' : 'Update DR'}
                  </span>
                </Button>
              )}
              {onBatchUpdateTraffic && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchUpdateTraffic}
                  disabled={updatingDR || updatingTraffic || discoveringLinks || !canMakeTrafficUpdate}
                  className="flex items-center gap-1"
                  title={!canMakeTrafficUpdate ? "Traffic updates are available for Professional users only" : "Update traffic data for all links"}
                >
                  {!canMakeTrafficUpdate && <Lock className="h-3 w-3" />}
                  {canMakeTrafficUpdate && <RefreshCw className={`h-3 w-3 ${updatingTraffic ? 'animate-spin' : ''}`} />}
                  <span className="text-xs">
                    {!canMakeTrafficUpdate ? 'Pro Only' : updatingTraffic ? 'Updating Traffic...' : 'Update Traffic'}
                  </span>
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Mobile Card View */}
      <div className="block md:hidden">
        <MobileCardView />
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block bg-background border border-border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead className="font-semibold">Link Details</TableHead>
              <TableHead className="font-semibold">Type</TableHead>
              <TableHead className="font-semibold">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('dr_score')}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  <span className="mr-1">DR</span>
                  {getSortIcon('dr_score')}
                </Button>
              </TableHead>
              <TableHead className="font-semibold">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('traffic')}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  <span className="mr-1">Traffic</span>
                  {getSortIcon('traffic')}
                </Button>
              </TableHead>
              <TableHead className="font-semibold w-20">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedLinks.map((link) => (
              <TableRow key={link.id} className="hover:bg-muted/20">
                <TableCell className="min-w-0">
                  <div className="flex items-center gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-foreground truncate">
                        {link.title}
                      </div>
                      <a 
                        href={`https://${getDomain(link)}`}
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-sm text-muted-foreground hover:text-primary transition-colors truncate block"
                      >
                        {getDomain(link)}
                      </a>
                    </div>
                    <a 
                      href={link.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors flex-shrink-0"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={link.link_type === 'paid' ? 'default' : 'secondary'}>
                    {link.link_type === 'paid' ? 'Paid' : 'Free'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className={`font-medium ${
                    sortField === 'dr_score' ? 'text-primary' : ''
                  }`}>
                    {link.dr_score}
                  </span>
                </TableCell>
                <TableCell>
                  <span className={`font-medium ${
                    sortField === 'traffic' ? 'text-primary' : ''
                  }`}>
                    {link.traffic?.toLocaleString() || '0'}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => onEditLink(link)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <DeleteConfirmationDialog
                      link={link}
                      onDelete={onDeleteLink}
                      trigger={
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      }
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 