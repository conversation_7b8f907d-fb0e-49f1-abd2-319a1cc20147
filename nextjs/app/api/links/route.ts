import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth";
import { createLinkResource, getLinkResourcesByUser } from "@/models/links";
import { Link, LinkResource } from "@/types/links";
import { normalizeUrl } from "@/utils/url-normalization";
import { validateTierAccess, createTierErrorResponse, createTierSuccessResponse } from "@/lib/tier-middleware";


export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user?.uuid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    const { data: links, error, count } = await getLinkResourcesByUser(user.uuid, limit, offset);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ 
      links, 
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    });
  } catch (error) {
    console.error("Error fetching links:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("POST /api/links - Starting request processing");

    // Validate tier access for link resource creation
    const tierResult = await validateTierAccess(request, { action: 'add_link_resource' });

    if (!tierResult.success) {
      console.log("POST /api/links - Tier validation failed:", tierResult);
      return createTierErrorResponse(tierResult);
    }

    const userUuid = tierResult.userUuid!;
    console.log("POST /api/links - User UUID:", userUuid);

    const body = await request.json();
    console.log("POST /api/links - Request body:", JSON.stringify(body, null, 2));

    // Validate and normalize URL
    if (!body.url) {
      console.log("POST /api/links - Missing URL in request body");
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    try {
      const normalizedUrl = normalizeUrl(body.url);
      console.log("POST /api/links - URL normalized successfully:", normalizedUrl);
      // URL normalization successful, continue with normalized URL
    } catch (error) {
      console.error("POST /api/links - URL normalization failed:", error);
      return NextResponse.json({ error: "Invalid URL format" }, { status: 400 });
    }

    // Create linkData for link_resources table (exclude fields that don't exist in the table)
    const linkData: Omit<LinkResource, 'id' | 'created_at' | 'updated_at'> = {
      url: body.url,
      title: body.title,
      link_type: body.link_type || 'free',
      price: body.price,
      currency: body.currency || 'USD',
      source: body.source,
      acquisition_method: body.acquisition_method,
      notes: body.notes,
      submit_url: body.submit_url,
      user_id: userUuid,
      last_checked: body.last_checked
    };

    console.log("POST /api/links - Creating link resource with data:", JSON.stringify(linkData, null, 2));
    const { data: link, error } = await createLinkResource(linkData);

    if (error) {
      console.error("POST /api/links - createLinkResource failed:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log("POST /api/links - Link resource created successfully:", link?.id);
    console.log("POST /api/links - Creating tier success response...");

    try {
      const response = await createTierSuccessResponse({ link }, userUuid);
      console.log("POST /api/links - Tier success response created successfully");
      return response;
    } catch (tierError) {
      console.error("POST /api/links - createTierSuccessResponse failed:", tierError);
      // Fallback to simple success response without usage info
      return NextResponse.json({ link }, { status: 201 });
    }
  } catch (error) {
    console.error("POST /api/links - Unexpected error:", error);
    console.error("POST /api/links - Error stack:", error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}